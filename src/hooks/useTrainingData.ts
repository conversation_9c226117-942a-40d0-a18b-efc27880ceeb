import { V1ManageTrainStudyRecordsAlarmDetailsStatisticsPost } from '@/api/api.req'

// 训练记录数据接口
export interface TrainingRecord {
  date: string
  workPassRate: string
  workCount: string
  workFailCount: string
  actionRate: string
  actionFailCount: string
  images?: {
    imageUrl: string
    description: string
    iconImg: string
    time: string
    recordDetailAlarmId?: string // 图片ID，用于懒加载
    loaded?: boolean // 是否已加载
  }[]
}

/**
 * 获取训练记录数据的通用函数
 * @param tabId 标签ID
 * @param callback 回调函数，用于处理获取到的记录数据
 */
export async function fetchTabDataCommon(
  tabId: string,
  callback: (records: TrainingRecord[]) => void,
) {
  try {
    const response = await V1ManageTrainStudyRecordsAlarmDetailsStatisticsPost({
      actionTypes: [],
      recordId: tabId,
      result: 0,
      standard: 0,
    })

    if (response && Array.isArray(response)) {
      // 转换数据格式
      const records: TrainingRecord[] = response.map((item: any) => ({
        date: item.createdDate || new Date().toLocaleDateString(),
        workPassRate: item.opNumOk && item.opNum ? Math.round((item.opNumOk / item.opNum) * 100).toString() : '0',
        workCount: item.opNum?.toString() || '0',
        workFailCount: item.opNum && item.opNumOk ? (item.opNum - item.opNumOk).toString() : '0',
        actionRate: item.actionNumOk && item.actionNum ? Math.round((item.actionNumOk / item.actionNum) * 100).toString() : '0',
        actionFailCount: item.actionNum && item.actionNumOk ? (item.actionNum - item.actionNumOk).toString() : '0',
        images: item.alarmItems?.map((alarm: any) => ({
          imageUrl: '', // 初始为空，需要通过 recordDetailAlarmId 查询
          description: `动作类型: ${alarm.actionType}`,
          iconImg: '/static/icon-standard.png',
          time: alarm.timestamp || '',
          recordDetailAlarmId: alarm.recordDetailAlarmId, // 用于后续查询图片
          loaded: false, // 标记是否已加载
        })) || [],
      }))

      callback(records)
    }
    else {
      callback([])
    }
  }
  catch (error) {
    console.error('获取训练记录数据失败:', error)
    callback([])
  }
}

/**
 * 格式化百分比数值
 * @param value 数值
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number | string): string {
  const num = typeof value === 'string' ? Number.parseFloat(value) : value
  return Number.isNaN(num) ? '0%' : `${Math.round(num)}%`
}

/**
 * 计算合格率
 * @param passed 合格数量
 * @param total 总数量
 * @returns 合格率百分比
 */
export function calculatePassRate(passed: number, total: number): number {
  if (total === 0)
    return 0
  return Math.floor((passed / total) * 100)
}

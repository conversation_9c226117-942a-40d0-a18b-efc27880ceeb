<script lang="ts" setup>
interface TrainingRecordImage {
  imageUrl: string
  description: string
  iconImg: string
  time: string
  isQualified?: boolean
  recordDetailAlarmId?: string
  loaded?: boolean
}

interface TrainingRecord {
  date: string
  workPassRate: string
  workCount: string
  workFailCount: string
  actionRate: string
  actionFailCount: string
  images?: TrainingRecordImage[]
  scrollLeft?: number
  activeImgIndex?: number
  visibleImageRange?: [number, number]
}

interface TrainingRecordsProps {
  /**
   * 记录列表
   */
  records: TrainingRecord[]
  /**
   * 标题
   * @default '训练记录'
   */
  title?: string
  /**
   * 是否显示图片
   * @default true
   */
  showImages?: boolean
}

const props = withDefaults(defineProps<TrainingRecordsProps>(), {
  title: '训练记录',
  showImages: true,
})

// 滚动视图引用
const recordScrollViewRefs = ref<HTMLElement[]>([])

// 格式化百分比
const formatPercentage = (value: string | number) => {
  const num = typeof value === 'string' ? parseFloat(value) : value
  return isNaN(num) ? '0%' : `${Math.round(num)}%`
}

// 初始化可见图片范围
const initVisibleRange = (recordIndex: number) => {
  const record = props.records[recordIndex]
  if (!record || !record.images || record.images.length === 0) return

  // 默认第一张图片可见
  record.visibleImageRange = [0, Math.min(1, record.images.length - 1)]
  record.activeImgIndex = 1
}

// 判断图片是否在可视范围内
const isImageVisible = (record: TrainingRecord, imgIndex: number) => {
  if (!record.visibleImageRange) return imgIndex === 0 // 默认显示第一张
  const [start, end] = record.visibleImageRange
  return imgIndex >= start && imgIndex <= end
}

// 处理图片滚动
const handleImageScroll = (event: Event, recordIndex: number) => {
  const target = event.target as HTMLElement
  const record = props.records[recordIndex]
  if (!record || !record.images || record.images.length <= 1) return

  const scrollLeft = target.scrollLeft
  const imageWidth = 240 // 图片宽度
  const gap = 12 // 间距

  // 计算当前显示的图片索引
  const currentIndex = Math.round(scrollLeft / (imageWidth + gap))
  record.activeImgIndex = currentIndex + 1

  // 更新可见图片范围
  const startIndex = Math.max(0, currentIndex - 1)
  const endIndex = Math.min(record.images.length - 1, currentIndex + 1)
  record.visibleImageRange = [startIndex, endIndex]
}

// 处理指示点点击
const handleIndicatorClick = (recordIndex: number, imgIndex: number) => {
  const record = props.records[recordIndex]
  if (!record || !record.images || record.images.length <= 1) return

  const imageWidth = 240
  const gap = 12
  const scrollLeft = imgIndex * (imageWidth + gap)

  record.activeImgIndex = imgIndex + 1
  record.visibleImageRange = [
    Math.max(0, imgIndex - 1),
    Math.min(record.images.length - 1, imgIndex + 1)
  ]

  // 滚动到对应图片
  const scrollView = recordScrollViewRefs.value[recordIndex]
  if (scrollView) {
    scrollView.scrollTo({ left: scrollLeft, behavior: 'smooth' })
  }
}

// 预览图片
const previewImages = ref<string[]>([])
const showImagePreview = ref(false)
const currentImageIndex = ref(0)

const handleImageClick = (images: TrainingRecordImage[], index: number) => {
  previewImages.value = images.map(img => img.imageUrl)
  currentImageIndex.value = index
  showImagePreview.value = true
}

// 初始化记录
onMounted(() => {
  props.records.forEach((record, index) => {
    if (record.images && record.images.length > 0) {
      initVisibleRange(index)
    }
  })
})
</script>

<template>
  <div class="records-section px-0 py-2 mb-8">
    <div class="section-title text-lg font-medium mb-4 px-4 text-gray-800">
      {{ title }}
    </div>

    <!-- 训练记录列表 -->
    <div class="record-list">
      <!-- 记录项 -->
      <div
        v-for="(record, index) in records"
        :key="index"
        class="record-item bg-white ml-4 mr-4 relative"
      >
        <!-- 记录内容 -->
        <div class="record-content pl-8 pr-4 py-4 relative">
          <!-- 左侧线条和圆点 -->
          <div class="record-left-border"></div>
          <div
            class="record-dot"
            :class="index === 0 ? 'record-dot-active' : 'record-dot-inactive'"
          ></div>

          <!-- 日期行 -->
          <div class="flex justify-between mb-2">
            <div class="flex items-center">
              <span
                class="text-sm font-medium"
                :class="index === 0 ? 'text-primary' : 'text-gray-800'"
              >
                {{ record.date }}
              </span>
            </div>
            <div>
              <span class="text-sm text-gray-500">作业次数：</span>
              <span class="text-sm font-medium text-primary">{{ record.workCount }}次</span>
            </div>
          </div>

          <!-- 数据统计网格 -->
          <div class="mb-3">
            <div class="flex justify-between mb-2">
              <div class="flex">
                <span class="text-xs text-gray-500">作业合格率：</span>
                <span class="text-xs font-medium text-gray-800">{{ formatPercentage(record.workPassRate) }}</span>
              </div>
              <div class="flex">
                <span class="text-xs text-gray-500">作业不合格次数：</span>
                <span class="text-xs font-medium text-red-500">{{ record.workFailCount }}次</span>
              </div>
            </div>
            <div class="flex justify-between">
              <div class="flex">
                <span class="text-xs text-gray-500">动作达标率：</span>
                <span class="text-xs font-medium text-gray-800">{{ formatPercentage(record.actionRate) }}</span>
              </div>
              <div class="flex">
                <span class="text-xs text-gray-500">动作不达标次数：</span>
                <span class="text-xs font-medium text-red-500">{{ record.actionFailCount }}次</span>
              </div>
            </div>
          </div>

          <!-- 图片滚动区域 -->
          <div
            v-if="showImages && record.images && record.images.length > 0"
            class="record-images-scroll mb-2"
          >
            <div
              class="flex overflow-x-auto scrollbar-hide"
              :ref="(el) => recordScrollViewRefs[index] = el as HTMLElement"
              @scroll="handleImageScroll($event, index)"
            >
              <div
                v-for="(img, imgIndex) in record.images"
                :key="imgIndex"
                class="record-image-item relative mr-3 w-60 h-32 flex-shrink-0 cursor-pointer"
                @click="handleImageClick(record.images!, imgIndex)"
              >
                <!-- 图片 -->
                <img
                  v-if="isImageVisible(record, imgIndex)"
                  :src="img.imageUrl"
                  :alt="img.description"
                  class="w-full h-full object-cover rounded-lg"
                  loading="lazy"
                />
                <div
                  v-else
                  class="w-full h-full bg-gray-100 flex items-center justify-center rounded-lg"
                >
                  <div class="loading-placeholder"></div>
                </div>

                <!-- 不合格图标 -->
                <img
                  v-if="img.iconImg"
                  :src="img.iconImg"
                  class="absolute top-0 right-0 w-11 h-11 z-10"
                />

                <!-- 图片信息 -->
                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 px-2 py-1 rounded-b-lg">
                  <div class="flex justify-between items-center">
                    <span class="text-white text-xs">{{ img.description }}</span>
                    <span class="text-white text-xs opacity-80">{{ img.time }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 指示点 -->
          <div
            v-if="record.images && record.images.length > 1"
            class="flex justify-center mt-2"
          >
            <div
              v-for="n in record.images.length"
              :key="n"
              class="mx-0.5 w-1.5 h-1.5 rounded-full cursor-pointer"
              :class="n === (record.activeImgIndex || 1) ? 'bg-primary' : 'bg-gray-300'"
              @click="handleIndicatorClick(index, n - 1)"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <VanImagePreview
      v-model:show="showImagePreview"
      :images="previewImages"
      :start-position="currentImageIndex"
      closeable
    />
  </div>
</template>

<style lang="less" scoped>
.records-section {
  /* 时间轴相关样式 */
  .record-item {
    position: relative;
    margin-bottom: 0;

    &:last-child {
      margin-bottom: 0;

      .record-left-border {
        bottom: 0;
      }
    }
  }

  .record-content {
    position: relative;
  }

  .record-left-border {
    position: absolute;
    top: 42px;
    bottom: -12px;
    left: 16px;
    width: 1px;
    background-color: #EAEDF0;
    z-index: 1;
  }

  .record-dot {
    position: absolute;
    top: 22px;
    left: 16px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transform: translateX(-50%);
    z-index: 2;
  }

  .record-dot-active {
    background-color: #00996B;
  }

  .record-dot-inactive {
    background-color: white;
    border: 1px solid #D8D8D8;
  }

  .record-images-scroll {
    margin-left: -2px;
    margin-right: -2px;
    padding: 2px;
  }

  .record-image-item {
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .loading-placeholder {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #00996B;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}
</style>

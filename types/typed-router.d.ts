/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/[...all]': RouteRecordInfo<'/[...all]', '/:all(.*)', { all: ParamValue<true> }, { all: ParamValue<false> }>,
    '/appointment/': RouteRecordInfo<'/appointment/', '/appointment', Record<never, never>, Record<never, never>>,
    '/appointment/add': RouteRecordInfo<'/appointment/add', '/appointment/add', Record<never, never>, Record<never, never>>,
    '/evaluate/': RouteRecordInfo<'/evaluate/', '/evaluate', Record<never, never>, Record<never, never>>,
    '/evaluate/detail': RouteRecordInfo<'/evaluate/detail', '/evaluate/detail', Record<never, never>, Record<never, never>>,
    '/exam/': RouteRecordInfo<'/exam/', '/exam', Record<never, never>, Record<never, never>>,
    '/exam/detail': RouteRecordInfo<'/exam/detail', '/exam/detail', Record<never, never>, Record<never, never>>,
    '/exam/score-dialog': RouteRecordInfo<'/exam/score-dialog', '/exam/score-dialog', Record<never, never>, Record<never, never>>,
    '/exam/select-test-paper': RouteRecordInfo<'/exam/select-test-paper', '/exam/select-test-paper', Record<never, never>, Record<never, never>>,
    '/exam/start-exam': RouteRecordInfo<'/exam/start-exam', '/exam/start-exam', Record<never, never>, Record<never, never>>,
    '/exam/view-answers': RouteRecordInfo<'/exam/view-answers', '/exam/view-answers', Record<never, never>, Record<never, never>>,
    '/home/': RouteRecordInfo<'/home/', '/home', Record<never, never>, Record<never, never>>,
    '/learning/': RouteRecordInfo<'/learning/', '/learning', Record<never, never>, Record<never, never>>,
    '/learning/detail': RouteRecordInfo<'/learning/detail', '/learning/detail', Record<never, never>, Record<never, never>>,
    '/login/': RouteRecordInfo<'/login/', '/login', Record<never, never>, Record<never, never>>,
    '/mine/': RouteRecordInfo<'/mine/', '/mine', Record<never, never>, Record<never, never>>,
    '/on-site-assessment/': RouteRecordInfo<'/on-site-assessment/', '/on-site-assessment', Record<never, never>, Record<never, never>>,
    '/on-site-assessment/view-answers-example': RouteRecordInfo<'/on-site-assessment/view-answers-example', '/on-site-assessment/view-answers-example', Record<never, never>, Record<never, never>>,
    '/redirect': RouteRecordInfo<'/redirect', '/redirect', Record<never, never>, Record<never, never>>,
    '/training/': RouteRecordInfo<'/training/', '/training', Record<never, never>, Record<never, never>>,
    '/training/competition-detail/': RouteRecordInfo<'/training/competition-detail/', '/training/competition-detail', Record<never, never>, Record<never, never>>,
    '/training/detail': RouteRecordInfo<'/training/detail', '/training/detail', Record<never, never>, Record<never, never>>,
    '/training/exam-detail/': RouteRecordInfo<'/training/exam-detail/', '/training/exam-detail', Record<never, never>, Record<never, never>>,
    '/training/report-detail/': RouteRecordInfo<'/training/report-detail/', '/training/report-detail', Record<never, never>, Record<never, never>>,
    '/workstation/info': RouteRecordInfo<'/workstation/info', '/workstation/info', Record<never, never>, Record<never, never>>,
  }
}
